# 修正后的Mermaid技术路线图

## 整体流程图

```mermaid
graph TD
    A["总体研究目标"] --> B["第一部分：回顾性队列研究"]
    B --> C["第二部分：生物学机制研究"]
    C --> D["第三部分：数据整合与未来规划"]
    D --> E["启动前瞻性队列与深化研究"]
    
    subgraph part1 ["第一部分"]
        B1["数据准备与队列构建"] --> B2["统计学分析"] 
        B2 --> B3["产出A"]
    end
    
    subgraph part2 ["第二部分"]
        C1["研究准备与方法学建立"] --> C2["样本采集与检测"] 
        C2 --> C3["产出B"]
    end
    
    subgraph part3 ["第三部分"]
        D1["整合分析与结论形成"] --> D2["成果转化与基金申请"] 
        D2 --> D3["启动前瞻性队列"]
    end

    A -->|"揭示PPIs与AD关联及机制"| B
    B3 -->|"临床关联证据"| D1
    C3 -->|"机制证据"| D1
    D1 -->|"构建证据链"| D2
    D2 -->|"资金支持"| D3
```

## 第一部分详细流程图

```mermaid
graph TD
    subgraph part1_detail ["第一部分：回顾性队列研究"]
        B1["数据准备与队列构建"] --> B2["统计学分析"]
        B2 --> B3["产出A"]
        
        B11["ADNI数据库权限申请"] --> B1
        B12["筛选MCI/AD患者"] --> B1
        B13["提取变量(CDoS/MPR, MMSE, p-Tau181)"] --> B1
        
        B21["PSM/IPTW平衡混杂因素"] --> B2
        B22["线性混合模型分析"] --> B2
        B23["H2RA对照验证偏倚"] --> B2
        
        B3 --> B31["明确临床关联"]
        B3 --> B32["估算效应量"]
    end
```

## 第二部分详细流程图

```mermaid
graph TD
    subgraph part2_detail ["第二部分：生物学机制研究"]
        C1["研究准备与方法学建立"] --> C2["样本采集与检测"]
        C2 --> C3["产出B"]
        
        C11["伦理审批/SOP制定"] --> C1
        C12["PBMCs分离技术优化"] --> C1
        C13["ELISA/Western Blot验证"] --> C1
        
        C21["招募40-60例患者"] --> C2
        C22["采集外周血样本"] --> C2
        C23["检测溶酶体/自噬标志物"] --> C2
        
        C3 --> C31["揭示溶酶体-自噬通路改变"]
        C3 --> C32["补充数据库分析证据"]
    end
```

## 第三部分详细流程图

```mermaid
graph TD
    subgraph part3_detail ["第三部分：数据整合与未来规划"]
        D1["整合分析与结论形成"] --> D2["成果转化与基金申请"]
        D2 --> D3["启动前瞻性队列"]
        
        D11["构建完整证据链"] --> D1
        D12["撰写高水平论著"] --> D1
        
        D21["撰写国家级课题申请"] --> D2
        D22["学术会议报告"] --> D2
        
        D3 --> D31["启动大样本前瞻性队列"]
        D3 --> D32["iPSC模型机制验证"]
        D31 --> E["深化研究"]
        D32 --> E
    end
```

## 修正说明

### 主要修正内容：

1. **子图命名规范化**：
   - 将中文子图名称改为英文标识符（如`part1`, `part2`, `part3`）
   - 使用方括号内的中文作为显示标题

2. **节点引用修正**：
   - 所有节点名称用双引号包围以支持中文
   - 修正了原代码中引用不存在节点的问题

3. **连接逻辑优化**：
   - 消除了循环引用（如`B2 -->|PSM/IPTW平衡混杂| B2`）
   - 优化了节点间的逻辑流向
   - 确保产出A和产出B正确连接到第三部分

4. **语法标准化**：
   - 箭头标签用双引号包围
   - 统一了节点命名规范
   - 确保Mermaid语法的正确性

### 使用方法：

1. 复制上述任意一个代码块
2. 粘贴到支持Mermaid的编辑器中（如Typora、VS Code、GitHub等）
3. 即可正确渲染流程图

这些修正后的代码现在可以在任何支持Mermaid的平台上正确渲染。
